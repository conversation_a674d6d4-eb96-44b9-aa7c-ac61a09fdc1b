import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../layout/DynamicAppHeader';
import { cn } from '../../utils/cn';
import { getAppById } from '../../data/mockApps';

// Mock user data
const mockUserData = {
  name: '<PERSON>',
  avatar: '👤',
  notifications: [
    { count: 3, icon: '🔔' },
  ],
};

export interface DynamicAppViewProps {
  className?: string;
  'data-testid'?: string;
}

const DynamicAppView: React.FC<DynamicAppViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { colors, isDark } = useThemeStore();

  const menuId = searchParams.get('menu');
  const viewId = searchParams.get('view') || 'dashboard';

  // Get app data based on menu ID
  const appData = menuId ? getAppById(menuId) : null;
  
  // Redirect to dashboard if no valid app is found
  useEffect(() => {
    if (!menuId || !appData) {
      navigate('/dashboard');
    }
  }, [menuId, appData, navigate]);
  
  if (!appData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2" style={{ color: colors.text }}>
            App not found
          </h2>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Redirecting to dashboard...
          </p>
        </div>
      </div>
    );
  }
  
  // Update nav links to show active state based on current view
  const updatedNavLinks = appData.navLinks ? appData.navLinks.map(link => ({
    ...link,
    isActive: link.href.includes(`view=${viewId}`),
  })) : [
    { label: 'Dashboard', href: `/app?menu=${menuId}&view=dashboard`, isActive: viewId === 'dashboard' },
  ];

  const currentView = appData.views ?
    (appData.views[viewId as keyof typeof appData.views] || appData.views.dashboard) :
    { title: `${appData.title} Dashboard`, content: `Welcome to ${appData.title}. This is the main dashboard view.` };
  
  // Create view data for the header
  const viewData = {
    title: currentView.title,
    actions: [
      { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true },
      { label: 'Import', onClick: () => console.log('Import clicked'), isPrimary: false },
    ],
    search: {
      filters: [
        { id: 'active', label: 'Active' },
        { id: 'archived', label: 'Archived' },
      ],
      onSearch: (query: string) => console.log('Search:', query),
      onRemoveFilter: (id: any) => console.log('Remove filter:', id),
    },
    pagination: {
      currentRange: '1-20 / 100',
      onNext: () => console.log('Next page'),
      onPrev: () => console.log('Previous page'),
    },
    viewModes: [
      { name: 'List', icon: '📋' },
      { name: 'Grid', icon: '⊞' },
      { name: 'Chart', icon: '📊' },
    ],
    activeViewMode: 'List',
  };
  
  const appHeaderData = {
    name: appData.title,
    icon: (
      <div
        className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-lg"
        style={{ backgroundColor: appData.color }}
      >
        {appData.icon}
      </div>
    ),
    navLinks: updatedNavLinks,
  };
  
  return (
    <div
      className={cn('min-h-screen', className)}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Dynamic App Header */}
      <DynamicAppHeader
        app={appHeaderData}
        user={mockUserData}
        view={viewData}
      />
      
      {/* Main Content Area */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm">
            <button
              onClick={() => navigate('/dashboard')}
              className="hover:underline"
              style={{ color: colors.textSecondary }}
            >
              Dashboard
            </button>
            <span style={{ color: colors.textSecondary }}>/</span>
            <span style={{ color: colors.text }}>{appData.title}</span>
            <span style={{ color: colors.textSecondary }}>/</span>
            <span style={{ color: colors.text }}>{currentView.title}</span>
          </nav>
          
          {/* Content Area */}
          <div
            className="rounded-lg border p-6"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
            }}
          >
            <div className="text-center py-12">
              <div
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl"
                style={{ backgroundColor: appData.color }}
              >
                {appData.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
                {currentView.title}
              </h3>
              <p className="text-sm max-w-md mx-auto" style={{ color: colors.textSecondary }}>
                {currentView.content}
              </p>
              <div className="mt-6">
                <button
                  className="px-4 py-2 rounded-lg text-white font-medium"
                  style={{ backgroundColor: appData.color }}
                  onClick={() => console.log('Get started clicked')}
                >
                  Get Started
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DynamicAppView;
