import React from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView, BaseViewProps } from '../BaseView/BaseView';
import { cn } from '../../../utils/cn';

export interface GridViewProps extends BaseViewProps {
  columns?: number;
  gap?: number;
  minItemWidth?: number;
  maxItemWidth?: number;
  aspectRatio?: number;
  selectable?: boolean;
  selectedItems?: string[];
  itemKey?: string | ((item: any) => string);
  onItemSelect?: (item: any, selected: boolean) => void;
  renderItem: (item: any, index: number) => React.ReactNode;
  renderItemActions?: (item: any, index: number) => React.ReactNode;
}

export const GridView: React.FC<GridViewProps> = ({
  data,
  columns,
  gap = 4,
  minItemWidth = 200,
  maxItemWidth,
  aspectRatio,
  selectable = false,
  selectedItems = [],
  itemKey = 'id',
  onItemSelect,
  renderItem,
  renderItemActions,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  const getItemKey = (item: any, index: number): string => {
    if (typeof itemKey === 'function') {
      return itemKey(item);
    }
    return item[itemKey] || index.toString();
  };

  const isSelected = (item: any): boolean => {
    const key = getItemKey(item, 0);
    return selectedItems.includes(key);
  };

  const handleItemSelect = (item: any, event: React.ChangeEvent<HTMLInputElement>) => {
    if (onItemSelect) {
      onItemSelect(item, event.target.checked);
    }
  };

  const getGridStyle = (): React.CSSProperties => {
    const style: React.CSSProperties = {
      display: 'grid',
      gap: `${gap * 0.25}rem`,
    };

    if (columns) {
      style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    } else {
      const minWidth = `${minItemWidth}px`;
      const maxWidth = maxItemWidth ? `${maxItemWidth}px` : '1fr';
      style.gridTemplateColumns = `repeat(auto-fill, minmax(${minWidth}, ${maxWidth}))`;
    }

    return style;
  };

  const getItemStyle = (): React.CSSProperties => {
    const style: React.CSSProperties = {};
    
    if (aspectRatio) {
      style.aspectRatio = aspectRatio.toString();
    }
    
    return style;
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div style={getGridStyle()}>
        {data.map((item, index) => {
          const key = getItemKey(item, index);
          const selected = isSelected(item);
          
          return (
            <div
              key={key}
              className={cn(
                'relative rounded-lg border transition-all duration-200',
                'hover:shadow-md hover:scale-[1.02]',
                selected && 'ring-2 ring-blue-500 ring-opacity-50',
                baseProps.onItemClick && 'cursor-pointer'
              )}
              style={{
                backgroundColor: colors.surface,
                borderColor: selected ? colors.primary : colors.border,
                ...getItemStyle(),
              }}
              onClick={() => baseProps.onItemClick?.(item, index)}
            >
              {/* Selection checkbox */}
              {selectable && (
                <div className="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    className="rounded"
                    checked={selected}
                    onChange={(e) => handleItemSelect(item, e)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              )}

              {/* Item actions */}
              {renderItemActions && (
                <div className="absolute top-2 right-2 z-10">
                  <div onClick={(e) => e.stopPropagation()}>
                    {renderItemActions(item, index)}
                  </div>
                </div>
              )}

              {/* Item content */}
              <div className="p-4 h-full">
                {renderItem(item, index)}
              </div>
            </div>
          );
        })}
      </div>
    </BaseView>
  );
};

export default GridView;
