import React, { useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView, BaseViewProps } from '../BaseView/BaseView';
import { GraphViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

export interface GraphViewProps extends BaseViewProps {
  config: GraphViewConfig;
  onDataPointClick?: (dataPoint: any, index: number) => void;
  renderTooltip?: (dataPoint: any) => React.ReactNode;
  renderLegend?: (datasets: any[]) => React.ReactNode;
}

export const GraphView: React.FC<GraphViewProps> = ({
  data,
  config,
  onDataPointClick,
  renderTooltip,
  renderLegend,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  // Process data for chart
  const chartData = useMemo((): ChartData => {
    const labels: string[] = [];
    const dataMap: { [key: string]: number[] } = {};

    if (config.groupBy) {
      // Group data by the specified field
      const groups: { [key: string]: any[] } = {};
      data.forEach(item => {
        const groupValue = item[config.groupBy!];
        if (!groups[groupValue]) {
          groups[groupValue] = [];
        }
        groups[groupValue].push(item);
      });

      // Process each group
      Object.keys(groups).forEach(groupKey => {
        const groupData = groups[groupKey];
        const xValues = [...new Set(groupData.map(item => item[config.xAxis]))].sort();
        
        if (labels.length === 0) {
          labels.push(...xValues.map(String));
        }

        dataMap[groupKey] = xValues.map(xValue => {
          const items = groupData.filter(item => item[config.xAxis] === xValue);
          if (items.length === 0) return 0;

          const yValues = Array.isArray(config.yAxis) ? config.yAxis : [config.yAxis];
          return yValues.reduce((sum, yField) => {
            const values = items.map(item => parseFloat(item[yField]) || 0);
            switch (config.aggregation) {
              case 'sum':
                return sum + values.reduce((a, b) => a + b, 0);
              case 'avg':
                return sum + (values.reduce((a, b) => a + b, 0) / values.length);
              case 'count':
                return sum + values.length;
              case 'min':
                return sum + Math.min(...values);
              case 'max':
                return sum + Math.max(...values);
              default:
                return sum + values.reduce((a, b) => a + b, 0);
            }
          }, 0);
        });
      });
    } else {
      // No grouping - single dataset
      const xValues = [...new Set(data.map(item => item[config.xAxis]))].sort();
      labels.push(...xValues.map(String));

      const yValues = Array.isArray(config.yAxis) ? config.yAxis : [config.yAxis];
      yValues.forEach(yField => {
        dataMap[yField] = xValues.map(xValue => {
          const items = data.filter(item => item[config.xAxis] === xValue);
          if (items.length === 0) return 0;

          const values = items.map(item => parseFloat(item[yField]) || 0);
          switch (config.aggregation) {
            case 'sum':
              return values.reduce((a, b) => a + b, 0);
            case 'avg':
              return values.reduce((a, b) => a + b, 0) / values.length;
            case 'count':
              return values.length;
            case 'min':
              return Math.min(...values);
            case 'max':
              return Math.max(...values);
            default:
              return values.reduce((a, b) => a + b, 0);
          }
        });
      });
    }

    // Create datasets
    const datasets = Object.keys(dataMap).map((key, index) => ({
      label: key,
      data: dataMap[key],
      backgroundColor: config.colors?.[index] || colors.primary,
      borderColor: config.colors?.[index] || colors.primary,
      borderWidth: 2,
    }));

    return { labels, datasets };
  }, [data, config, colors.primary]);

  // Simple bar chart renderer (placeholder for actual chart library)
  const renderBarChart = () => {
    const maxValue = Math.max(...chartData.datasets.flatMap(d => d.data));
    const chartHeight = 300;

    return (
      <div className="space-y-4">
        {/* Chart area */}
        <div className="relative" style={{ height: chartHeight }}>
          <div className="flex items-end justify-around h-full p-4 border rounded-lg" style={{ borderColor: colors.border }}>
            {chartData.labels.map((label, labelIndex) => (
              <div key={label} className="flex flex-col items-center space-y-2">
                <div className="flex items-end space-x-1" style={{ height: chartHeight - 60 }}>
                  {chartData.datasets.map((dataset, datasetIndex) => {
                    const value = dataset.data[labelIndex];
                    const height = (value / maxValue) * (chartHeight - 80);
                    
                    return (
                      <div
                        key={datasetIndex}
                        className="cursor-pointer hover:opacity-80 transition-opacity"
                        style={{
                          width: '20px',
                          height: `${height}px`,
                          backgroundColor: dataset.backgroundColor,
                          minHeight: value > 0 ? '2px' : '0px',
                        }}
                        onClick={() => onDataPointClick?.({ label, value, dataset: dataset.label }, labelIndex)}
                        title={`${dataset.label}: ${value}`}
                      />
                    );
                  })}
                </div>
                <span className="text-xs text-center" style={{ color: colors.textSecondary }}>
                  {label}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Y-axis labels */}
        <div className="flex justify-between text-xs" style={{ color: colors.textSecondary }}>
          <span>0</span>
          <span>{Math.round(maxValue / 2)}</span>
          <span>{maxValue}</span>
        </div>
      </div>
    );
  };

  // Simple pie chart renderer
  const renderPieChart = () => {
    const total = chartData.datasets[0]?.data.reduce((a, b) => a + b, 0) || 0;
    let currentAngle = 0;
    const radius = 100;
    const centerX = 120;
    const centerY = 120;

    return (
      <div className="flex items-center justify-center">
        <svg width="240" height="240" className="mr-8">
          {chartData.labels.map((label, index) => {
            const value = chartData.datasets[0]?.data[index] || 0;
            const percentage = (value / total) * 100;
            const angle = (value / total) * 360;
            
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            currentAngle += angle;

            const startAngleRad = (startAngle * Math.PI) / 180;
            const endAngleRad = (endAngle * Math.PI) / 180;

            const x1 = centerX + radius * Math.cos(startAngleRad);
            const y1 = centerY + radius * Math.sin(startAngleRad);
            const x2 = centerX + radius * Math.cos(endAngleRad);
            const y2 = centerY + radius * Math.sin(endAngleRad);

            const largeArcFlag = angle > 180 ? 1 : 0;

            const pathData = [
              `M ${centerX} ${centerY}`,
              `L ${x1} ${y1}`,
              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
              'Z'
            ].join(' ');

            return (
              <path
                key={index}
                d={pathData}
                fill={config.colors?.[index] || colors.primary}
                stroke={colors.background}
                strokeWidth="2"
                className="cursor-pointer hover:opacity-80"
                onClick={() => onDataPointClick?.({ label, value, percentage }, index)}
              />
            );
          })}
        </svg>

        {/* Legend */}
        <div className="space-y-2">
          {chartData.labels.map((label, index) => {
            const value = chartData.datasets[0]?.data[index] || 0;
            const percentage = ((value / total) * 100).toFixed(1);
            
            return (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: config.colors?.[index] || colors.primary }}
                />
                <span className="text-sm" style={{ color: colors.text }}>
                  {label}: {value} ({percentage}%)
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Simple line chart renderer
  const renderLineChart = () => {
    const maxValue = Math.max(...chartData.datasets.flatMap(d => d.data));
    const chartHeight = 300;
    const chartWidth = 600;

    return (
      <div className="space-y-4">
        <svg width={chartWidth} height={chartHeight} className="border rounded-lg" style={{ borderColor: colors.border }}>
          {/* Grid lines */}
          {config.showGrid && (
            <g>
              {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
                <line
                  key={ratio}
                  x1="40"
                  y1={40 + (chartHeight - 80) * ratio}
                  x2={chartWidth - 40}
                  y2={40 + (chartHeight - 80) * ratio}
                  stroke={colors.border}
                  strokeDasharray="2,2"
                />
              ))}
            </g>
          )}

          {/* Data lines */}
          {chartData.datasets.map((dataset, datasetIndex) => {
            const points = dataset.data.map((value, index) => {
              const x = 40 + ((chartWidth - 80) / (chartData.labels.length - 1)) * index;
              const y = 40 + (chartHeight - 80) * (1 - value / maxValue);
              return `${x},${y}`;
            }).join(' ');

            return (
              <g key={datasetIndex}>
                <polyline
                  points={points}
                  fill="none"
                  stroke={dataset.borderColor}
                  strokeWidth={dataset.borderWidth}
                />
                {/* Data points */}
                {dataset.data.map((value, index) => {
                  const x = 40 + ((chartWidth - 80) / (chartData.labels.length - 1)) * index;
                  const y = 40 + (chartHeight - 80) * (1 - value / maxValue);
                  
                  return (
                    <circle
                      key={index}
                      cx={x}
                      cy={y}
                      r="4"
                      fill={dataset.backgroundColor}
                      className="cursor-pointer hover:r-6"
                      onClick={() => onDataPointClick?.({ 
                        label: chartData.labels[index], 
                        value, 
                        dataset: dataset.label 
                      }, index)}
                    />
                  );
                })}
              </g>
            );
          })}

          {/* X-axis labels */}
          {chartData.labels.map((label, index) => {
            const x = 40 + ((chartWidth - 80) / (chartData.labels.length - 1)) * index;
            return (
              <text
                key={index}
                x={x}
                y={chartHeight - 10}
                textAnchor="middle"
                fontSize="12"
                fill={colors.textSecondary}
              >
                {label}
              </text>
            );
          })}
        </svg>
      </div>
    );
  };

  const renderChart = () => {
    switch (config.chartType) {
      case 'bar':
        return renderBarChart();
      case 'pie':
      case 'donut':
        return renderPieChart();
      case 'line':
      case 'area':
        return renderLineChart();
      default:
        return renderBarChart();
    }
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="p-6">
        {/* Chart */}
        {renderChart()}

        {/* Legend */}
        {config.showLegend && chartData.datasets.length > 1 && (
          <div className="mt-6 flex flex-wrap gap-4">
            {chartData.datasets.map((dataset, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: dataset.backgroundColor }}
                />
                <span className="text-sm" style={{ color: colors.text }}>
                  {dataset.label}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </BaseView>
  );
};

export default GraphView;
