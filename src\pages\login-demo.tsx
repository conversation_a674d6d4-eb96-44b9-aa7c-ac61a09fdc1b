import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '../stores/themeStore';
import { LoginDemo } from '../components/LoginDemo';
import { ThemeToggle } from '../components/ThemeToggle';
import { Button, Heading, Text } from '../components/ui';
import type { User } from '../services/authService';

const LoginDemoPage: React.FC = () => {
  const navigate = useNavigate();
  const { colors } = useThemeStore();

  const handleLoginSuccess = (user: User) => {
    console.log('User logged in:', user);
    // Optionally navigate to dashboard after login
    // navigate('/dashboard');
  };

  return (
    <div
      className="min-h-screen flex flex-col"
      style={{ backgroundColor: colors.background }}
    >
      {/* Header */}
      <header className="border-b" style={{ borderColor: colors.border }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="text-sm"
              >
                ← Back to Home
              </Button>
              <div className="h-6 w-px" style={{ backgroundColor: colors.border }} />
              <Heading level={5} style={{ color: colors.text }}>
                Authentication Demo
              </Heading>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center space-y-2">
            <Heading level={2} className="mb-2">
              Mock Authentication Service
            </Heading>
            <Text variant="body1" color="secondary" className="max-w-sm mx-auto">
              Test the authentication service with different user roles and permissions.
            </Text>
          </div>

          <LoginDemo
            onLoginSuccess={handleLoginSuccess}
            data-testid="login-demo"
          />

          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard')}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default LoginDemoPage;
