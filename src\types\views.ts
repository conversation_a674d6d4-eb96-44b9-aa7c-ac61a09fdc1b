import { ReactNode } from 'react';

// Base interfaces for all views
export interface BaseViewConfig {
  id: string;
  name: string;
  icon: ReactNode;
  description?: string;
  isAvailable?: boolean;
  permissions?: string[];
}

export interface ViewAction {
  id: string;
  label: string;
  icon?: ReactNode;
  onClick: () => void;
  isPrimary?: boolean;
  isDisabled?: boolean;
  tooltip?: string;
  shortcut?: string;
}

export interface ViewFilter {
  id: string;
  label: string;
  value?: any;
  type?: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: { label: string; value: any }[];
}

export interface ViewSort {
  field: string;
  direction: 'asc' | 'desc';
  label?: string;
}

export interface ViewPagination {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  currentRange: string;
  onNext: () => void;
  onPrev: () => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export interface ViewSearch {
  query: string;
  placeholder?: string;
  filters: ViewFilter[];
  sorts: ViewSort[];
  onSearch: (query: string) => void;
  onFilterAdd: (filter: ViewFilter) => void;
  onFilterRemove: (filterId: string) => void;
  onFilterUpdate: (filterId: string, value: any) => void;
  onSortChange: (sort: ViewSort) => void;
  onClearAll: () => void;
}

// Specific view type interfaces
export interface KanbanViewConfig extends BaseViewConfig {
  type: 'kanban';
  columns: {
    id: string;
    title: string;
    color?: string;
    limit?: number;
    isCollapsed?: boolean;
  }[];
  cardFields: string[];
  groupByField: string;
  swimlanes?: {
    field: string;
    values: string[];
  };
}

export interface CalendarViewConfig extends BaseViewConfig {
  type: 'calendar';
  dateField: string;
  endDateField?: string;
  titleField: string;
  colorField?: string;
  viewModes: ('month' | 'week' | 'day' | 'agenda')[];
  defaultViewMode: 'month' | 'week' | 'day' | 'agenda';
  timeSlots?: {
    start: string;
    end: string;
    interval: number;
  };
}

export interface PivotViewConfig extends BaseViewConfig {
  type: 'pivot';
  rows: string[];
  columns: string[];
  values: {
    field: string;
    aggregation: 'sum' | 'count' | 'avg' | 'min' | 'max';
    label?: string;
  }[];
  filters?: string[];
  showTotals?: boolean;
  showSubtotals?: boolean;
}

export interface GraphViewConfig extends BaseViewConfig {
  type: 'graph';
  chartType: 'bar' | 'line' | 'pie' | 'scatter' | 'area' | 'donut' | 'radar';
  xAxis: string;
  yAxis: string | string[];
  groupBy?: string;
  aggregation?: 'sum' | 'count' | 'avg' | 'min' | 'max';
  colors?: string[];
  showLegend?: boolean;
  showGrid?: boolean;
}

export interface ActivityViewConfig extends BaseViewConfig {
  type: 'activity';
  timestampField: string;
  titleField: string;
  descriptionField?: string;
  userField?: string;
  typeField?: string;
  groupBy?: 'date' | 'user' | 'type';
  showTimeline?: boolean;
  activityTypes?: {
    [key: string]: {
      icon: ReactNode;
      color: string;
      label: string;
    };
  };
}

export interface MapViewConfig extends BaseViewConfig {
  type: 'map';
  latitudeField: string;
  longitudeField: string;
  titleField: string;
  descriptionField?: string;
  colorField?: string;
  sizeField?: string;
  clusterMarkers?: boolean;
  mapStyle?: 'roadmap' | 'satellite' | 'hybrid' | 'terrain';
  defaultZoom?: number;
  defaultCenter?: [number, number];
}

export interface GanttViewConfig extends BaseViewConfig {
  type: 'gantt';
  taskNameField: string;
  startDateField: string;
  endDateField: string;
  progressField?: string;
  dependenciesField?: string;
  resourceField?: string;
  parentField?: string;
  showCriticalPath?: boolean;
  showBaseline?: boolean;
  timeScale?: 'day' | 'week' | 'month' | 'quarter';
}

export interface RelationshipViewConfig extends BaseViewConfig {
  type: 'relationship';
  nodeIdField: string;
  nodeLabelField: string;
  edgeSourceField: string;
  edgeTargetField: string;
  edgeLabelField?: string;
  nodeColorField?: string;
  nodeSizeField?: string;
  layout?: 'force' | 'hierarchical' | 'circular' | 'grid';
  showLabels?: boolean;
  enableZoom?: boolean;
}

export interface HierarchicalViewConfig extends BaseViewConfig {
  type: 'hierarchical';
  idField: string;
  parentField: string;
  labelField: string;
  childrenField?: string;
  iconField?: string;
  expandedByDefault?: boolean;
  showLines?: boolean;
  allowDragDrop?: boolean;
  maxDepth?: number;
}

export interface FormViewConfig extends BaseViewConfig {
  type: 'form';
  recordId?: string;
  fields: {
    name: string;
    label: string;
    type: string;
    required?: boolean;
    readonly?: boolean;
    section?: string;
  }[];
  sections?: {
    name: string;
    label: string;
    collapsible?: boolean;
    collapsed?: boolean;
  }[];
  layout?: 'single' | 'two-column' | 'tabs';
}

export interface DashboardViewConfig extends BaseViewConfig {
  type: 'dashboard';
  widgets: {
    id: string;
    type: string;
    title: string;
    position: { x: number; y: number; w: number; h: number };
    config: any;
  }[];
  layout?: 'grid' | 'masonry';
  columns?: number;
  allowResize?: boolean;
  allowMove?: boolean;
}

export interface LayoutViewConfig extends BaseViewConfig {
  type: 'layout';
  templateType: 'invoice' | 'report' | 'email' | 'document';
  elements: {
    id: string;
    type: string;
    position: { x: number; y: number; w: number; h: number };
    properties: any;
  }[];
  pageSize?: 'A4' | 'Letter' | 'Legal' | 'Custom';
  orientation?: 'portrait' | 'landscape';
}

// Union type for all view configurations
export type ViewConfig = 
  | KanbanViewConfig
  | CalendarViewConfig
  | PivotViewConfig
  | GraphViewConfig
  | ActivityViewConfig
  | MapViewConfig
  | GanttViewConfig
  | RelationshipViewConfig
  | HierarchicalViewConfig
  | FormViewConfig
  | DashboardViewConfig
  | LayoutViewConfig;

// View state interface
export interface ViewState {
  currentView: string;
  availableViews: ViewConfig[];
  viewData: any[];
  isLoading: boolean;
  error?: string;
  search: ViewSearch;
  pagination: ViewPagination;
  selectedItems: string[];
  bulkActions: ViewAction[];
}

// View context for data and actions
export interface ViewContext {
  data: any[];
  loading: boolean;
  error?: string;
  refresh: () => void;
  create: (item: any) => Promise<void>;
  update: (id: string, item: any) => Promise<void>;
  delete: (id: string) => Promise<void>;
  bulkUpdate: (ids: string[], updates: any) => Promise<void>;
  bulkDelete: (ids: string[]) => Promise<void>;
  export: (format: 'csv' | 'excel' | 'pdf') => Promise<void>;
}
