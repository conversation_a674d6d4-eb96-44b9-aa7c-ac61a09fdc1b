import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import RootLayout from '../pages/layout';
import HomePage from '../pages/index';
import DemoPage from '../pages/demo';
import DashboardPage from '../pages/dashboard';
import AppHeaderDemoPage from '../pages/app-header-demo';
import AppPage from '../pages/app';
import LoginDemoPage from '../pages/login-demo';

// Define routes using React Router v6
const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'demo',
        element: <DemoPage />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'app-header-demo',
        element: <AppHeaderDemoPage />,
      },
      {
        path: 'app',
        element: <AppPage />,
      },
      {
        path: 'login-demo',
        element: <LoginDemoPage />,
      },
    ],
  },
]);

export function AppRouter() {
  return <RouterProvider router={router} />;
}
